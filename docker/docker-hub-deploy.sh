#!/bin/bash

set -e  # Stop on error

# Stop and remove existing containers
echo "Stopping existing containers..."
docker compose down
docker system prune -f

# Build and start containers
echo "Building and starting containers..."
docker compose -f docker-compose.dev.yaml up -d --build

# Tag the images
echo "Tagging images..."
docker tag docker-web xzneozx96/mf-ai-staging-web:latest
docker tag docker-api xzneozx96/mf-ai-staging-api:latest

# Push to Docker Hub
echo "Pushing images to Docker Hub..."
docker push xzneozx96/mf-ai-staging-web:latest
docker push xzneozx96/mf-ai-staging-api:latest

echo "✅ Deployment complete."

# Stop and remove existing containers to reduce resource usage
echo "Stopping existing containers..."
docker compose down
docker system prune -f
