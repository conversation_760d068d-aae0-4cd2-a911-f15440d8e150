{"name": "dify-web", "version": "1.4.3", "private": true, "engines": {"node": ">=v22.11.0"}, "browserslist": ["last 1 Chrome version", "last 1 Firefox version", "last 1 Edge version", "last 1 Safari version", "iOS >=15", "Android >= 10", "and_chr >= 126", "and_ff >= 137", "and_uc >= 15.5", "and_qq >= 14.9"], "scripts": {"dev": "cross-env NODE_OPTIONS='--max-old-space-size=6096' next dev", "build": "next build", "start": "cp -r .next/static .next/standalone/.next/static && cp -r public .next/standalone/public && cross-env PORT=$npm_config_port HOSTNAME=$npm_config_host node .next/standalone/server.js", "lint": "pnpm eslint --cache --cache-location node_modules/.cache/eslint/.eslint-cache", "lint-only-show-error": "pnpm eslint --cache --cache-location node_modules/.cache/eslint/.eslint-cache --quiet", "fix": "next lint --fix", "eslint-fix": "eslint --cache --cache-location node_modules/.cache/eslint/.eslint-cache --fix", "eslint-fix-only-show-error": "eslint --cache --cache-location node_modules/.cache/eslint/.eslint-cache --fix --quiet", "eslint-complexity": "eslint --rule 'complexity: [error, {max: 15}]' --quiet", "prepare": "cd ../ && node -e \"if (process.env.NODE_ENV !== 'production'){process.exit(1)} \" || husky ./web/.husky", "gen-icons": "node ./app/components/base/icons/script.mjs", "uglify-embed": "node ./bin/uglify-embed", "check-i18n": "node ./i18n/check-i18n.js", "auto-gen-i18n": "node ./i18n/auto-gen-i18n.js", "test": "jest", "test:watch": "jest --watch", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "preinstall": "npx only-allow pnpm"}, "dependencies": {"@babel/runtime": "^7.22.3", "@dagrejs/dagre": "^1.1.4", "@emoji-mart/data": "^1.2.1", "@eslint/compat": "^1.2.4", "@floating-ui/react": "^0.26.25", "@formatjs/intl-localematcher": "^0.5.6", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.0.16", "@hookform/resolvers": "^3.9.0", "@lexical/code": "^0.30.0", "@lexical/link": "^0.30.0", "@lexical/list": "^0.30.0", "@lexical/react": "^0.30.0", "@lexical/selection": "^0.30.0", "@lexical/text": "^0.30.0", "@lexical/utils": "^0.30.0", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@monaco-editor/react": "^4.6.0", "@next/mdx": "15.2.3", "@octokit/core": "^6.1.2", "@octokit/request-error": "^6.1.5", "@remixicon/react": "^4.5.0", "@sentry/react": "^8.54.0", "@sentry/utils": "^8.54.0", "@svgdotjs/svg.js": "^3.2.4", "@tailwindcss/typography": "^0.5.15", "@tanstack/react-form": "^1.3.3", "@tanstack/react-query": "^5.60.5", "@tanstack/react-query-devtools": "^5.60.5", "abcjs": "^6.4.4", "ahooks": "^3.8.4", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "copy-to-clipboard": "^3.3.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "decimal.js": "^10.4.3", "dompurify": "^3.2.4", "echarts": "^5.5.1", "echarts-for-react": "^3.0.2", "elkjs": "^0.9.3", "emoji-mart": "^5.5.2", "fast-deep-equal": "^3.1.3", "globals": "^15.11.0", "html-to-image": "1.11.11", "i18next": "^23.16.4", "i18next-resources-to-backend": "^1.2.1", "immer": "^9.0.19", "js-audio-recorder": "^1.0.7", "js-cookie": "^3.0.5", "jsonschema": "^1.5.0", "jwt-decode": "^4.0.0", "katex": "^0.16.21", "ky": "^1.7.2", "lamejs": "^1.2.1", "lexical": "^0.30.0", "line-clamp": "^1.0.0", "lodash-es": "^4.17.21", "mermaid": "11.4.1", "mime": "^4.0.4", "mitt": "^3.0.1", "negotiator": "^0.6.3", "next": "15.2.3", "next-themes": "^0.4.3", "pinyin-pro": "^3.25.0", "qrcode.react": "^4.2.0", "qs": "^6.13.0", "react": "19.0.0", "react-18-input-autosize": "^3.0.0", "react-dom": "19.0.0", "react-easy-crop": "^5.1.0", "react-error-boundary": "^4.1.2", "react-headless-pagination": "^1.1.6", "react-hook-form": "^7.53.1", "react-hotkeys-hook": "^4.6.1", "react-i18next": "^15.1.0", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^9.0.1", "react-multi-email": "^1.0.25", "react-papaparse": "^4.4.0", "react-pdf-highlighter": "^8.0.0-rc.0", "react-slider": "^2.0.6", "react-sortablejs": "^6.1.4", "react-syntax-highlighter": "^15.6.1", "react-textarea-autosize": "^8.5.8", "react-tooltip": "5.8.3", "react-window": "^1.8.10", "react-window-infinite-loader": "^1.0.9", "reactflow": "^11.11.3", "recordrtc": "^5.6.2", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "scheduler": "^0.23.0", "semver": "^7.6.3", "server-only": "^0.0.1", "sharp": "^0.33.2", "shave": "^5.0.4", "sortablejs": "^1.15.0", "swr": "^2.3.0", "tailwind-merge": "^2.5.4", "use-context-selector": "^2.0.0", "uuid": "^10.0.0", "zod": "^3.23.8", "zundo": "^2.1.0", "zustand": "^4.5.2"}, "devDependencies": {"@antfu/eslint-config": "^4.1.1", "@chromatic-com/storybook": "^3.1.0", "@eslint-react/eslint-plugin": "^1.15.0", "@eslint/eslintrc": "^3.1.0", "@eslint/js": "^9.20.0", "@faker-js/faker": "^9.0.3", "@happy-dom/jest-environment": "^17.4.4", "@next/eslint-plugin-next": "^15.2.3", "@rgrove/parse-xml": "^4.1.0", "@storybook/addon-essentials": "8.5.0", "@storybook/addon-interactions": "8.5.0", "@storybook/addon-links": "8.5.0", "@storybook/addon-onboarding": "8.5.0", "@storybook/addon-themes": "8.5.0", "@storybook/blocks": "8.5.0", "@storybook/nextjs": "8.5.0", "@storybook/react": "8.5.0", "@storybook/test": "8.5.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.2", "@testing-library/react": "^16.0.1", "@types/crypto-js": "^4.2.2", "@types/dagre": "^0.7.52", "@types/jest": "^29.5.13", "@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/negotiator": "^0.6.3", "@types/node": "18.15.0", "@types/qs": "^6.9.16", "@types/react": "19.0.11", "@types/react-dom": "19.0.4", "@types/react-slider": "^1.3.6", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-window": "^1.8.8", "@types/react-window-infinite-loader": "^1.0.9", "@types/recordrtc": "^5.6.14", "@types/semver": "^7.5.8", "@types/sortablejs": "^1.15.1", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "bing-translate-api": "^4.0.2", "code-inspector-plugin": "^0.18.1", "cross-env": "^7.0.3", "eslint": "^9.20.1", "eslint-config-next": "^15.0.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-sonarjs": "^3.0.2", "eslint-plugin-storybook": "^0.11.2", "eslint-plugin-tailwindcss": "^3.18.0", "husky": "^9.1.6", "jest": "^29.7.0", "lint-staged": "^15.2.10", "lodash": "^4.17.21", "magicast": "^0.3.4", "postcss": "^8.4.47", "sass": "^1.80.3", "storybook": "8.5.0", "tailwindcss": "^3.4.14", "ts-node": "^10.9.2", "typescript": "4.9.5", "typescript-eslint": "^8.23.0", "uglify-js": "^3.19.3"}, "resolutions": {"@types/react": "~18.2.0", "@types/react-dom": "~18.2.0", "string-width": "4.2.3"}, "lint-staged": {"**/*.js?(x)": ["eslint --fix"], "**/*.ts?(x)": ["eslint --fix"]}, "overrides": {"@types/react": "19.0.11", "@types/react-dom": "19.0.4", "@storybook/test": "8.5.0"}, "pnpm": {"overrides": {"esbuild@<0.25.0": "0.25.0"}}}