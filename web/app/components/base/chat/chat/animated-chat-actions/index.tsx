'use client'

import type { <PERSON> } from 'react'
import { memo } from 'react'
import cn from '@/utils/classnames'
import s from './style.module.css'

export type AnimationState = 'recording' | 'generating' | 'idle'

export type AnimatedChatActionsProps = {
  /**
   * Current animation state to display
   */
  state: AnimationState
  /**
   * Additional CSS classes
   */
  className?: string
  /**
   * Whether the component is disabled
   */
  disabled?: boolean
  /**
   * Accessibility label for screen readers
   */
  ariaLabel?: string
}

const AnimatedChatActions: FC<AnimatedChatActionsProps> = ({
  state = 'idle',
  className,
  disabled = false,
  ariaLabel,
}) => {
  const getAnimationContent = () => {
    switch (state) {
      case 'recording':
        return (
          <div className="relative flex items-center justify-center">
            {/* Listening Icon */}
            <button>
              <img className='w-[360px]' src={'/3D-character-GUI/listen.png'} />
            </button>

            {/* Sound Wave Animation */}
            <div className={cn(s['sound-waves'], 'absolute inset-0')}>
              <div className={cn(s.wave, s['wave-1'])} />
              <div className={cn(s.wave, s['wave-2'])} />
              <div className={cn(s.wave, s['wave-3'])} />
            </div>
          </div>
        )

      case 'generating':
        return (
          <div className="relative flex items-center justify-center">
            {/* Thinking Icon */}
            <button>
              <img className='w-[360px]' src={'/3D-character-GUI/thinking.png'} />
            </button>

            {/* Pulse Animation */}
            <div className={cn(s['pulse-animation'], 'absolute inset-0')}>
              <div className={cn(s.pulse, s['pulse-1'])} />
              <div className={cn(s.pulse, s['pulse-2'])} />
            </div>
          </div>
        )

      case 'idle':
        return (
          <div className="relative flex items-center justify-center">
            {/* Microphone Icon */}
            <button>
              <img className='w-[360px]' src={'/3D-character-GUI/micro.png'} />
            </button>

            {/* Sound Wave Animation */}
            <div className={cn(s['sound-waves'], 'absolute inset-0')}>
              <div className={cn(s.wave, s['wave-1'])} />
              <div className={cn(s.wave, s['wave-2'])} />
              <div className={cn(s.wave, s['wave-3'])} />
            </div>
          </div>
        )

      default:
        return null
    }
  }

  const getAriaLabel = () => {
    if (ariaLabel) return ariaLabel

    switch (state) {
      case 'recording':
        return 'Recording audio'
      case 'generating':
        return 'Generating response'
      case 'idle':
        return 'Chat actions'
      default:
        return 'Chat actions'
    }
  }

  if (state === 'idle') return null

  return (
    <div
      className={cn(
        'flex items-center justify-center rounded-full',
        'border border-primary-200 bg-primary-25',
        'transition-all duration-200 ease-in-out',
        disabled && 'pointer-events-none opacity-50',
        className,
      )}
      role="status"
      aria-label={getAriaLabel()}
      aria-live="polite"
    >
      {getAnimationContent()}
    </div>
  )
}

export default memo(AnimatedChatActions)
