'use client'

import type { FC } from 'react'
import { memo, useCallback, useEffect, useRef, useState } from 'react'
import cn from '@/utils/classnames'
import s from './style.module.css'
import { useParams, usePathname } from 'next/navigation'
import Recorder from 'js-audio-recorder'
import { audioToText } from '@/service/share'
import { StopCircle } from '@/app/components/base/icons/src/vender/solid/mediaAndDevices'
import { convertToMp3 } from '@/app/components/base/voice-input/utils'
import type { OnSend } from '../../types'
import { RiCloseLine, RiLoader2Line } from '@remixicon/react'

export type AnimationState = 'recording' | 'generating' | 'idle'

export type AnimatedChatActionsProps = {
  /**
   * Additional CSS classes
   */
  className?: string
  /**
   * Whether the component is disabled
   */
  disabled?: boolean

  isResponding?: boolean

  onSend?: OnSend
}

const AnimatedChatActions: FC<AnimatedChatActionsProps> = ({
  className,
  disabled = false,
  isResponding,
  onSend,
}) => {
  const [chatState, setChatState] = useState<AnimationState>('idle')
  const [startRecord, setStartRecord] = useState(false)
  const [startConvert, setStartConvert] = useState(false)
  const [showAnswerImage, setShowAnswerImage] = useState(false)

  const pathname = usePathname()
  const params = useParams()

  const recorder = useRef(new Recorder({
    sampleBits: 16,
    sampleRate: 16000,
    numChannels: 1,
    compiling: false,
  }))
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  // Effect to handle the 3-second timer for switching to answer image
  useEffect(() => {
    // Clear any existing timer
    if (timerRef.current) {
      clearTimeout(timerRef.current)
    }

    // Reset showAnswerImage when state changes
    setShowAnswerImage(false)

    // Start timer for non-idle states
    if (chatState !== 'idle' || isResponding) {
      timerRef.current = setTimeout(() => {
        setShowAnswerImage(true)
      }, 3000)
    }

    // Cleanup function
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current)
        timerRef.current = null
      }
    }
  }, [chatState, isResponding])

  const handleStopRecorder = useCallback(async () => {
    setStartRecord(false)
    setStartConvert(true)
    recorder.current.stop()
    const mp3Blob = convertToMp3(recorder.current)
    const mp3File = new File([mp3Blob], 'temp.mp3', { type: 'audio/mp3' })
    const formData = new FormData()
    formData.append('file', mp3File)
    formData.append('word_timestamps', 'disabled')

    let url = ''
    let isPublic = false

    if (params.token) {
      url = '/audio-to-text'
      isPublic = true
    }
    else if (params.appId) {
      if (pathname.search('explore/installed') > -1)
        url = `/installed-apps/${params.appId}/audio-to-text`
      else
        url = `/apps/${params.appId}/audio-to-text`
    }

    try {
      const audioResponse = await audioToText(url, isPublic, formData)
      onSend?.(audioResponse.text)
      setChatState('idle')
    }
    catch {
      onSend?.('')
      setChatState('idle')
    }
  }, [onSend, params.appId, params.token, pathname])

  const handleStartRecord = async () => {
    try {
      await recorder.current.start()
      setStartRecord(true)
      setStartConvert(false)
      setChatState('recording')
    }
    catch {
      setChatState('idle')
    }
  }

  const getAnimationContent = () => {
    if (isResponding) {
      return <div className="relative flex items-center justify-center">
        {/* Thinking/Answer Icon */}
        <button>
          <img
            className='w-[360px]'
            src={showAnswerImage ? '/3D-character-GUI/answer.png' : '/3D-character-GUI/thinking.png'}
          />
        </button>

        {/* Sound Wave Animation */}
        <div className={cn(s['sound-waves'], 'absolute inset-0')}>
          <div className={cn(s.wave, s['wave-1'])} />
          <div className={cn(s.wave, s['wave-2'])} />
        </div>
      </div>
    }
    else {
      switch (chatState) {
        case 'recording':
          return (
            <>
              <div className="relative flex items-center justify-center">
                {/* Listening/Answer Icon */}
                <button>
                  <img
                    className='w-[360px]'
                    src={showAnswerImage ? '/3D-character-GUI/answer.png' : '/3D-character-GUI/listen.png'}
                  />
                </button>

                {/* Sound Wave Animation */}
                <div className={cn(s['sound-waves'], 'absolute inset-0')}>
                  <div className={cn(s.wave, s['wave-1'])} />
                  <div className={cn(s.wave, s['wave-2'])} />
                </div>
              </div>

              <div className='z-20'>
                {
                  startRecord && (
                    <div
                      className='mr-1 flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg  hover:bg-primary-100'
                      onClick={handleStopRecorder}
                    >
                      <StopCircle className='h-5 w-5 text-primary-600' />
                    </div>
                  )
                }

                {
                  startConvert && <RiLoader2Line className='mr-2 h-4 w-4 animate-spin text-primary-700' />
                }

                {
                  startConvert && (
                    <div
                      className='mr-1 flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg  hover:bg-gray-200'
                      onClick={() => setChatState('idle')}
                    >
                      <RiCloseLine className='h-4 w-4 text-gray-500' />
                    </div>
                  )
                }
              </div>
            </>
          )

        case 'generating':
          return (
            <div className="relative flex items-center justify-center">
              {/* Thinking/Answer Icon */}
              <button>
                <img
                  className='w-[360px]'
                  src={showAnswerImage ? '/3D-character-GUI/answer.png' : '/3D-character-GUI/thinking.png'}
                />
              </button>

              {/* Pulse Animation */}
              <div className={cn(s['pulse-animation'], 'absolute inset-0')}>
                <div className={cn(s.pulse, s['pulse-1'])} />
                <div className={cn(s.pulse, s['pulse-2'])} />
              </div>
            </div>
          )

        case 'idle':
          return (
            <div className="relative flex items-center justify-center">
              {/* Microphone Icon */}
              <button className='z-10' onClick={handleStartRecord}>
                <img className='w-[360px]' src={'/3D-character-GUI/micro.png'} />
              </button>

              {/* Sound Wave Animation */}
              <div className={cn(s['sound-waves'], 'absolute inset-0')}>
                <div className={cn(s.wave, s['wave-1'])} />
                <div className={cn(s.wave, s['wave-2'])} />
              </div>
            </div>
          )

        default:
          return null
      }
    }
  }

  const getAriaLabel = () => {
    switch (chatState) {
      case 'recording':
        return 'Recording audio'
      case 'generating':
        return 'Generating response'
      case 'idle':
        return 'Chat actions'
      default:
        return 'Chat actions'
    }
  }

  return (
    <div
      className={cn(
        disabled && 'pointer-events-none opacity-50',
        className,
      )}
      role="status"
      aria-label={getAriaLabel()}
      aria-live="polite"
    >
      {getAnimationContent()}
    </div>
  )
}

export default memo(AnimatedChatActions)
