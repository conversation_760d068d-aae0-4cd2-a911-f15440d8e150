'use client'

import type { FC } from 'react'
import { memo, useCallback, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams, usePathname } from 'next/navigation'
import Recorder from 'js-audio-recorder'
import { RiLoader2Line } from '@remixicon/react'
import cn from '@/utils/classnames'
import { audioToText } from '@/service/share'
import { StopCircle } from '@/app/components/base/icons/src/vender/solid/mediaAndDevices'
import { convertToMp3 } from '@/app/components/base/voice-input/utils'
import Button from '../../../button'
import type { OnSend } from '../../types'
import s from './style.module.css'

type AnimationState = 'recording' | 'generating' | 'idle'

type AnimatedChatActionsProps = {
  isResponding?: boolean
  noStopResponding?: boolean
  onStopResponding?: () => void
  onSend?: OnSend
  className?: string
}

// Image sources for different states
const IMAGE_SOURCES = {
  idle: '/3D-character-GUI/micro.png',
  recording: '/3D-character-GUI/listen.png',
  thinking: '/3D-character-GUI/thinking.png',
  answer: '/3D-character-GUI/answer.png',
} as const

const AnimatedChatActions: FC<AnimatedChatActionsProps> = ({
  isResponding,
  noStopResponding,
  onStopResponding,
  onSend,
  className,
}) => {
  const { t } = useTranslation()
  const pathname = usePathname()
  const params = useParams()

  // State management
  const [chatState, setChatState] = useState<AnimationState>('idle')
  const [isRecording, setIsRecording] = useState(false)
  const [isConverting, setIsConverting] = useState(false)

  // Refs
  const recorder = useRef(new Recorder({
    sampleBits: 16,
    sampleRate: 16000,
    numChannels: 1,
    compiling: false,
  }))
  const imgRef = useRef<HTMLImageElement>(null)
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current)
        clearTimeout(timerRef.current)
    }
  }, [])

  const handleStopRecorder = useCallback(async () => {
    setIsRecording(false)
    setIsConverting(true)
    recorder.current.stop()
    const mp3Blob = convertToMp3(recorder.current)
    const mp3File = new File([mp3Blob], 'temp.mp3', { type: 'audio/mp3' })
    const formData = new FormData()
    formData.append('file', mp3File)
    formData.append('word_timestamps', 'disabled')

    let url = ''
    let isPublic = false

    if (params.token) {
      url = '/audio-to-text'
      isPublic = true
    }
    else if (params.appId) {
      if (pathname.search('explore/installed') > -1)
        url = `/installed-apps/${params.appId}/audio-to-text`
      else
        url = `/apps/${params.appId}/audio-to-text`
    }

    try {
      const audioResponse = await audioToText(url, isPublic, formData)
      onSend?.(audioResponse.text)
      setChatState('idle')
    }
    catch {
      onSend?.('')
      setChatState('idle')
    }
  }, [onSend, params.appId, params.token, pathname])

  // Helper function to setup image switching timer
  const setupImageTimer = useCallback((initialSrc: string, targetSrc: string, delay: number = 2000) => {
    // Clear existing timer
    if (timerRef.current)
      clearTimeout(timerRef.current)

    // Set new timer
    timerRef.current = setTimeout(() => {
      if (imgRef.current)
        imgRef.current.src = targetSrc
    }, delay)

    return initialSrc
  }, [])

  const handleStartRecord = async () => {
    try {
      await recorder.current.start()
      setIsRecording(true)
      setIsConverting(false)
      setChatState('recording')
    }
    catch {
      setChatState('idle')
    }
  }

  const getAnimationContent = () => {
    if (isResponding) {
      return (
        <>
          <div className="relative flex items-center justify-center">
            {/* Thinking Icon */}
            <button className='z-10'>
              <img
                ref={imgRef}
                className='w-[360px]'
                src={setupImageTimer(IMAGE_SOURCES.thinking, IMAGE_SOURCES.answer)}
              />
            </button>

            {/* Sound Wave Animation */}
            <div className={cn(s['sound-waves'], 'absolute inset-0')}>
              <div className={cn(s.wave, s['wave-1'])} />
              <div className={cn(s.wave, s['wave-2'])} />
            </div>
          </div>

          <div className='z-20'>
            {
              !noStopResponding && (
                <div className='stop-respond-action mb-2 flex justify-center'>
                  <Button onClick={onStopResponding}>
                    <StopCircle className='mr-2 size-10 text-red-500' />
                    <span className='text-xl font-normal text-red-500'>{t('appDebug.operation.stopResponding')}</span>
                  </Button>
                </div>
              )
            }
          </div>
        </>
      )
    }
    else {
      switch (chatState) {
        case 'recording':
          return (
            <>
              <div className="relative flex items-center justify-center">
                {/* Listening Icon */}
                <button className='z-10'>
                  <img className='w-[360px]' src={'/3D-character-GUI/listen.png'} />
                </button>

                {/* Sound Wave Animation */}
                <div className={cn(s['sound-waves'], 'absolute inset-0')}>
                  <div className={cn(s.wave, s['wave-1'])} />
                  <div className={cn(s.wave, s['wave-2'])} />
                </div>
              </div>

              <div className='z-20'>
                {
                  isRecording && (
                    <div
                      className='mx-auto flex size-20 cursor-pointer items-center justify-center rounded-lg'
                      onClick={handleStopRecorder}
                    >
                      <StopCircle className='size-full text-red-600' />
                    </div>
                  )
                }

                {
                  isConverting && (
                    <div className='mx-auto flex size-20 cursor-pointer items-center justify-center rounded-lg'>
                      <RiLoader2Line className='size-full animate-spin text-primary-700' />
                    </div>
                  )
                }
              </div>
            </>
          )

        case 'idle':
          return (
            <>
              <div className="relative flex items-center justify-center">
                {/* Microphone Icon */}
                <button className='z-10' onClick={handleStartRecord}>
                  <img className='w-[360px]' src={'/3D-character-GUI/micro.png'} />
                </button>

                {/* Sound Wave Animation */}
                <div className={cn(s['sound-waves'], 'absolute inset-0')}>
                  <div className={cn(s.wave, s['wave-1'])} />
                  <div className={cn(s.wave, s['wave-2'])} />
                </div>
              </div>
            </>
          )

        default:
          return null
      }
    }
  }

  const getAriaLabel = () => {
    switch (chatState) {
      case 'recording':
        return 'Recording audio'
      case 'generating':
        return 'Generating response'
      case 'idle':
        return 'Chat actions'
      default:
        return 'Chat actions'
    }
  }

  return (
    <div
      className={cn(
        'font-sans', // This will use the Inter font we just added
        className,
      )}
      role="status"
      aria-label={getAriaLabel()}
      aria-live="polite"
    >
      {getAnimationContent()}
    </div>
  )
}

export default memo(AnimatedChatActions)
