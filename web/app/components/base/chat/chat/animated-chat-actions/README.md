# Animated Chat Actions Component

A state-driven animated UI component that displays different animated elements based on the current conversation state.

## Features

- **State-driven animations**: Displays different animations based on the current state
- **Extensible design**: Easy to add new animation states
- **Responsive**: Works across different screen sizes
- **Accessible**: Includes proper ARIA labels and supports reduced motion preferences
- **TypeScript**: Full type safety with proper interfaces

## Usage

```tsx
import AnimatedChatActions from '@/app/components/base/chat/chat/animated-chat-actions'

// Basic usage
<AnimatedChatActions state="recording" />

// With custom size and styling
<AnimatedChatActions 
  state="generating" 
  size="large"
  className="my-custom-class"
  ariaLabel="Custom accessibility label"
/>

// Disabled state
<AnimatedChatActions 
  state="recording" 
  disabled={true}
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `state` | `'recording' \| 'generating' \| 'idle'` | Required | Current animation state to display |
| `className` | `string` | `undefined` | Additional CSS classes |
| `size` | `'small' \| 'medium' \| 'large'` | `'medium'` | Size of the component |
| `disabled` | `boolean` | `false` | Whether the component is disabled |
| `ariaLabel` | `string` | Auto-generated | Accessibility label for screen readers |

## Animation States

### Recording State
- Displays an animated microphone icon
- Shows expanding sound wave rings
- Indicates active audio recording

### Generating State
- Displays an animated speaker icon
- Shows pulsing animation
- Indicates AI response generation

### Idle State
- Returns `null` (component is hidden)
- Use when no action is taking place

## Accessibility

- Includes proper ARIA labels and roles
- Supports `aria-live="polite"` for screen readers
- Respects `prefers-reduced-motion` setting
- High contrast mode support

## Customization

The component uses CSS modules for animations and Tailwind CSS for styling. You can:

1. **Override styles**: Pass custom classes via the `className` prop
2. **Add new states**: Extend the `AnimationState` type and add new cases to the switch statement
3. **Customize animations**: Modify the CSS module file for different animation effects

## Future Extensions

The component is designed to easily accommodate new animation states:

```tsx
// Example of adding a new 'thinking' state
case 'thinking':
  return (
    <div className="relative flex items-center justify-center">
      <ThinkingIcon className={iconSizeClasses[size]} />
      <div className={s['thinking-animation']}>
        {/* Custom thinking animation */}
      </div>
    </div>
  )
```

## Browser Support

- Modern browsers with CSS animation support
- Graceful degradation for older browsers
- Reduced motion support for accessibility
