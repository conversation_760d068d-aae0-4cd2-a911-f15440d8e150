/* Sound Wave Animation for Recording State */
.sound-waves {
  display: flex;
  align-items: center;
  justify-content: center;
}

.wave {
  position: absolute;
  border-radius: 50%;
  border: 2px solid rgba(21, 94, 239, 0.3);
  animation: soundWave 2s infinite ease-out;
}

.wave-1 {
  animation-delay: 0s;
}

.wave-2 {
  animation-delay: 0.4s;
}

.wave-3 {
  animation-delay: 0.8s;
}

@keyframes soundWave {
  0% {
    width: 200px;
    height: 200px;
    opacity: 1;
    border-width: 3px;
  }
  50% {
    opacity: 0.7;
    border-width: 2px;
  }
  100% {
    width: 120%;
    height: 120%;
    opacity: 0;
    border-width: 1px;
  }
}

/* Pulse Animation for Generating State */
.pulse-animation {
  display: flex;
  align-items: center;
  justify-content: center;
}

.pulse {
  position: absolute;
  border-radius: 50%;
  background: rgba(21, 94, 239, 0.2);
  animation: pulse 1.5s infinite ease-in-out;
}

.pulse-1 {
  animation-delay: 0s;
}

.pulse-2 {
  animation-delay: 0.75s;
}

@keyframes pulse {
  0% {
    width: 200px;
    height: 200px;
    opacity: 1;
  }
  50% {
    width: 80%;
    height: 80%;
    opacity: 0.5;
  }
  100% {
    width: 120%;
    height: 120%;
    opacity: 0;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .wave {
    border-color: rgba(21, 94, 239, 0.8);
  }

  .pulse {
    background: rgba(21, 94, 239, 0.6);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .wave,
  .pulse {
    animation: none;
  }

  .wave {
    width: 100%;
    height: 100%;
    opacity: 0.3;
  }

  .pulse {
    width: 80%;
    height: 80%;
    opacity: 0.3;
  }
}
