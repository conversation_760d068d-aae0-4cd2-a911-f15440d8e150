import {
  useNodesReadOnly,
} from '../hooks'
import Divider from '../../base/divider'
import RunAndHistory from './run-and-history'
import EnvButton from './env-button'
import { useAppContext } from '@/context/app-context'

export type HeaderInNormalProps = {
  components?: {
    left?: React.ReactNode
    middle?: React.ReactNode
  }
}
const HeaderInNormal = ({
  components,
}: HeaderInNormalProps) => {
  const { nodesReadOnly } = useNodesReadOnly()
  const { isCurrentWorkspaceOwner } = useAppContext()

  return (
    <>
      <div className='flex items-center gap-2'>
        {isCurrentWorkspaceOwner && (
          <>
            {components?.left}
            <EnvButton disabled={nodesReadOnly} />
            <Divider type='vertical' className='mx-auto h-3.5' />
            <RunAndHistory />
          </>
        )}
        {components?.middle}
      </div>
    </>
  )
}

export default HeaderInNormal
