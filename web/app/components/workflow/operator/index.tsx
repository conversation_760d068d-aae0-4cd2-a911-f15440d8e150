import { memo } from 'react'
import { MiniMap } from 'reactflow'
import UndoRedo from '../header/undo-redo'
import ZoomInOut from './zoom-in-out'
import Control from './control'
import { useAppContext } from '@/context/app-context'
import cn from '@/utils/classnames'

export type OperatorProps = {
  handleUndo: () => void
  handleRedo: () => void
}

const Operator = ({ handleUndo, handleRedo }: OperatorProps) => {
  const { isCurrentWorkspaceOwner } = useAppContext()
  return (
    <>
      <MiniMap
        pannable
        zoomable
        style={{
          width: 102,
          height: 72,
        }}
        maskColor='var(--color-workflow-minimap-bg)'
        className={cn('!absolute !bottom-14 !left-4 z-[9] !m-0 !h-[72px] !w-[102px] !rounded-lg !border-[0.5px] !border-divider-subtle !bg-background-default-subtle !shadow-md !shadow-shadow-shadow-5', !isCurrentWorkspaceOwner && 'hidden')}
      />
      <div className={cn('absolute bottom-4 left-4 z-[9] mt-1 flex items-center gap-2', !isCurrentWorkspaceOwner && 'hidden')}>
        <ZoomInOut />
        <UndoRedo handleUndo={handleUndo} handleRedo={handleRedo} />
        <Control />
      </div>
    </>
  )
}

export default memo(Operator)
