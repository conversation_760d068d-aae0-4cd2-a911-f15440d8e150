# Simplified Workflow System Implementation

## Overview

This document outlines the implementation of a simplified workflow system based on <PERSON><PERSON>'s workflow architecture. Unlike Dify's dynamic workflow system with complex branching, this implementation focuses on a single, predefined flow with sequential steps that require human approval/triggering for each step before moving forward.

## Workflow Structure

The workflow consists of a series of tasks, each potentially containing multiple steps. The structure is predefined and linear, with no dynamic branching.

### Task and Step Structure

```typescript
enum EStatusTask {
  PENDING = "pending",
  IN_PROGRESS = "in_progress",
  COMPLETED = "completed",
  FAILED = "failed",
}

enum ETypeStep {
  FORM = "form",
  EVALUATION = "evaluation",
  OUTCOME = "outcome",
  ANALYSIS = "analysis",
  GENERATION = "generation",
}

interface Step {
  id: string;
  name: string;
  description: string;
  status: EStatusTask;
  type: ETypeStep;
}

interface Task {
  id: string;
  name: string;
  description: string;
  status: EStatusTask;
  steps: Step[];
}
```

## Database Models

### Mongoose Schema Definitions

```javascript
// models/workflow.js
const mongoose = require("mongoose");
const { Schema } = mongoose;
const { v4: uuidv4 } = require("uuid");

// Step Schema
const stepSchema = new Schema({
  id: {
    type: String,
    default: uuidv4,
  },
  name: {
    type: String,
    required: true,
  },
  description: String,
  type: {
    type: String,
    enum: ["form", "evaluation", "outcome", "analysis", "generation"],
    required: true,
  },
  status: {
    type: String,
    enum: ["pending", "in_progress", "completed", "failed"],
    default: "pending",
  },
  position: {
    type: Number,
    required: true,
  },
  data: {
    type: Schema.Types.Mixed,
    default: {},
  },
  created_at: {
    type: Date,
    default: Date.now,
  },
  updated_at: {
    type: Date,
    default: Date.now,
  },
});

// Task Schema
const taskSchema = new Schema({
  id: {
    type: String,
    default: uuidv4,
  },
  name: {
    type: String,
    required: true,
  },
  description: String,
  status: {
    type: String,
    enum: ["pending", "in_progress", "completed", "failed"],
    default: "pending",
  },
  position: {
    type: Number,
    required: true,
  },
  steps: [stepSchema],
  created_at: {
    type: Date,
    default: Date.now,
  },
  updated_at: {
    type: Date,
    default: Date.now,
  },
});

// Workflow Schema
const workflowSchema = new Schema({
  id: {
    type: String,
    default: uuidv4,
  },
  name: {
    type: String,
    required: true,
  },
  description: String,
  tasks: [taskSchema],
  created_at: {
    type: Date,
    default: Date.now,
  },
  updated_at: {
    type: Date,
    default: Date.now,
  },
});

// Pre-save middleware to update the 'updated_at' field
workflowSchema.pre("save", function (next) {
  this.updated_at = Date.now();
  next();
});

taskSchema.pre("save", function (next) {
  this.updated_at = Date.now();
  next();
});

stepSchema.pre("save", function (next) {
  this.updated_at = Date.now();
  next();
});

// Create models
const Workflow = mongoose.model("Workflow", workflowSchema);
const Task = mongoose.model("Task", taskSchema);
const Step = mongoose.model("Step", stepSchema);

module.exports = { Workflow, Task, Step };
```

## Workflow Engine Implementation

The workflow engine manages the state and progression of the workflow:

```javascript
// services/workflowEngine.js
const { Workflow, Task, Step } = require("../models/workflow");

class WorkflowEngine {
  /**
   * Create a new workflow with predefined tasks and steps
   * @param {Object} workflowData - Workflow creation data
   * @param {string} workflowData.name - Workflow name
   * @param {string} workflowData.description - Workflow description
   * @param {Array} workflowData.tasks - Array of task data
   * @returns {Promise<Object>} Created workflow
   */
  async createWorkflow(workflowData) {
    const { name, description, tasks: tasksData = [] } = workflowData;

    // Create workflow with tasks and steps
    const workflow = new Workflow({
      name,
      description,
      tasks: tasksData.map((taskData, position) => ({
        id: taskData.id,
        name: taskData.name,
        description: taskData.description,
        status: "pending",
        position,
        steps: (taskData.steps || []).map((stepData, stepPosition) => ({
          id: stepData.id,
          name: stepData.name,
          description: stepData.description,
          type: stepData.type,
          status: "pending",
          position: stepPosition,
        })),
      })),
    });

    // Save workflow to database
    await workflow.save();
    return workflow;
  }

  /**
   * Get workflow by ID
   * @param {string} workflowId - Workflow ID
   * @returns {Promise<Object>} Workflow with tasks and steps
   */
  async getWorkflow(workflowId) {
    return await Workflow.findOne({ id: workflowId });
  }

  /**
   * Get the current active task in the workflow
   * @param {string} workflowId - Workflow ID
   * @returns {Promise<Object>} Current active task
   */
  async getCurrentTask(workflowId) {
    const workflow = await Workflow.findOne({ id: workflowId });
    if (!workflow) {
      throw new Error(`Workflow with ID ${workflowId} not found`);
    }

    // Find the first pending or in-progress task
    return workflow.tasks.find(
      (task) => task.status === "pending" || task.status === "in_progress"
    );
  }

  /**
   * Get the current active step in a task
   * @param {string} workflowId - Workflow ID
   * @param {string} taskId - Task ID
   * @returns {Promise<Object>} Current active step
   */
  async getCurrentStep(workflowId, taskId) {
    const workflow = await Workflow.findOne({ id: workflowId });
    if (!workflow) {
      throw new Error(`Workflow with ID ${workflowId} not found`);
    }

    const task = workflow.tasks.find((t) => t.id === taskId);
    if (!task) {
      throw new Error(`Task with ID ${taskId} not found`);
    }

    // Find the first pending or in-progress step
    return task.steps.find(
      (step) => step.status === "pending" || step.status === "in_progress"
    );
  }

  /**
   * Get a step by its ID
   * @param {string} workflowId - Workflow ID
   * @param {string} stepId - Step ID
   * @returns {Promise<Object>} Step object and its parent task
   */
  async getStep(workflowId, stepId) {
    const workflow = await Workflow.findOne({ id: workflowId });
    if (!workflow) {
      throw new Error(`Workflow with ID ${workflowId} not found`);
    }

    // Find the task containing the step
    for (const task of workflow.tasks) {
      const step = task.steps.find((s) => s.id === stepId);
      if (step) {
        return { step, task };
      }
    }

    throw new Error(`Step with ID ${stepId} not found`);
  }

  /**
   * Get a step by its position in a task
   * @param {string} workflowId - Workflow ID
   * @param {string} taskId - Task ID
   * @param {number} position - Step position
   * @returns {Promise<Object>} Step at the specified position
   */
  async getStepByPosition(workflowId, taskId, position) {
    const workflow = await Workflow.findOne({ id: workflowId });
    if (!workflow) {
      throw new Error(`Workflow with ID ${workflowId} not found`);
    }

    const task = workflow.tasks.find((t) => t.id === taskId);
    if (!task) {
      throw new Error(`Task with ID ${taskId} not found`);
    }

    const step = task.steps.find((s) => s.position === position);
    if (!step) {
      throw new Error(`Step at position ${position} not found`);
    }

    return step;
  }

  /**
   * Start a task and its first step
   * @param {string} workflowId - Workflow ID
   * @param {string} taskId - Task ID
   * @returns {Promise<Object>} Updated task
   */
  async startTask(workflowId, taskId) {
    const workflow = await Workflow.findOne({ id: workflowId });
    if (!workflow) {
      throw new Error(`Workflow with ID ${workflowId} not found`);
    }

    const taskIndex = workflow.tasks.findIndex((t) => t.id === taskId);
    if (taskIndex === -1) {
      throw new Error(`Task with ID ${taskId} not found`);
    }

    const task = workflow.tasks[taskIndex];
    if (task.status !== "pending") {
      throw new Error(`Task with ID ${taskId} is not in pending status`);
    }

    // Update task status
    task.status = "in_progress";

    // Start the first step if it exists
    if (task.steps.length > 0) {
      task.steps[0].status = "in_progress";
    }

    await workflow.save();
    return task;
  }

  /**
   * Complete a step and move to the next step or task
   * @param {string} workflowId - Workflow ID
   * @param {string} stepId - Step ID
   * @param {Object} data - Step data to save
   * @returns {Promise<Object>} Updated step
   */
  async completeStep(workflowId, stepId, data = null) {
    const workflow = await Workflow.findOne({ id: workflowId });
    if (!workflow) {
      throw new Error(`Workflow with ID ${workflowId} not found`);
    }

    // Find the task and step
    let targetTask = null;
    let targetStep = null;
    let targetStepIndex = -1;
    let targetTaskIndex = -1;

    for (let i = 0; i < workflow.tasks.length; i++) {
      const task = workflow.tasks[i];
      const stepIndex = task.steps.findIndex((s) => s.id === stepId);

      if (stepIndex !== -1) {
        targetTask = task;
        targetStep = task.steps[stepIndex];
        targetStepIndex = stepIndex;
        targetTaskIndex = i;
        break;
      }
    }

    if (!targetStep) {
      throw new Error(`Step with ID ${stepId} not found`);
    }

    if (targetStep.status !== "in_progress") {
      throw new Error(`Step with ID ${stepId} is not in progress`);
    }

    // Update step status and data
    targetStep.status = "completed";
    if (data) {
      targetStep.data = data;
    }

    // Find the next step in the task
    const nextStepIndex = targetTask.steps.findIndex(
      (s) => s.position > targetStep.position && s.status === "pending"
    );

    if (nextStepIndex !== -1) {
      // Move to the next step
      targetTask.steps[nextStepIndex].status = "in_progress";
    } else {
      // Complete the task if no more steps
      targetTask.status = "completed";

      // Find the next task in the workflow
      const nextTaskIndex = workflow.tasks.findIndex(
        (t) => t.position > targetTask.position && t.status === "pending"
      );

      // The next task remains in pending status until explicitly started
    }

    await workflow.save();
    return targetStep;
  }

  /**
   * Update step data
   * @param {string} workflowId - Workflow ID
   * @param {string} stepId - Step ID
   * @param {Object} data - Data to update
   * @returns {Promise<Object>} Updated step
   */
  async updateStepData(workflowId, stepId, data) {
    const workflow = await Workflow.findOne({ id: workflowId });
    if (!workflow) {
      throw new Error(`Workflow with ID ${workflowId} not found`);
    }

    // Find the step
    let targetStep = null;

    for (const task of workflow.tasks) {
      const stepIndex = task.steps.findIndex((s) => s.id === stepId);

      if (stepIndex !== -1) {
        targetStep = task.steps[stepIndex];
        task.steps[stepIndex].data = data;
        break;
      }
    }

    if (!targetStep) {
      throw new Error(`Step with ID ${stepId} not found`);
    }

    await workflow.save();
    return targetStep;
  }
}

module.exports = WorkflowEngine;
```

## API Implementation

### Express Router Setup

```javascript
// routes/workflow.js
const express = require("express");
const router = express.Router();
const WorkflowEngine = require("../services/workflowEngine");

const workflowEngine = new WorkflowEngine();

// Error handler middleware
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch((err) => {
    res.status(500).json({ error: err.message });
  });
};

/**
 * Create a new workflow
 * POST /api/workflows
 */
router.post(
  "/workflows",
  asyncHandler(async (req, res) => {
    const { name, description, tasks } = req.body;

    const workflow = await workflowEngine.createWorkflow({
      name,
      description,
      tasks,
    });

    res.status(201).json({
      id: workflow.id,
      name: workflow.name,
      description: workflow.description,
    });
  })
);

/**
 * Get workflow by ID
 * GET /api/workflows/:workflowId
 */
router.get(
  "/workflows/:workflowId",
  asyncHandler(async (req, res) => {
    const { workflowId } = req.params;

    const workflow = await workflowEngine.getWorkflow(workflowId);

    if (!workflow) {
      return res.status(404).json({ error: "Workflow not found" });
    }

    // Format response
    const formattedWorkflow = {
      id: workflow.id,
      name: workflow.name,
      description: workflow.description,
      tasks: workflow.tasks.map((task) => ({
        id: task.id,
        name: task.name,
        description: task.description,
        status: task.status,
        steps: task.steps.map((step) => ({
          id: step.id,
          name: step.name,
          description: step.description,
          type: step.type,
          status: step.status,
          data: step.data,
        })),
      })),
    };

    res.json(formattedWorkflow);
  })
);

/**
 * Start a task
 * POST /api/workflows/:workflowId/tasks/:taskId/start
 */
router.post(
  "/workflows/:workflowId/tasks/:taskId/start",
  asyncHandler(async (req, res) => {
    const { workflowId, taskId } = req.params;

    try {
      const task = await workflowEngine.startTask(workflowId, taskId);

      res.json({
        id: task.id,
        name: task.name,
        status: task.status,
      });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  })
);

/**
 * Complete a step
 * POST /api/workflows/:workflowId/steps/:stepId/complete
 */
router.post(
  "/workflows/:workflowId/steps/:stepId/complete",
  asyncHandler(async (req, res) => {
    const { workflowId, stepId } = req.params;
    const { data } = req.body;

    try {
      const step = await workflowEngine.completeStep(workflowId, stepId, data);

      res.json({
        id: step.id,
        name: step.name,
        status: step.status,
        data: step.data,
      });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  })
);

/**
 * Update step data
 * PUT /api/workflows/:workflowId/steps/:stepId/data
 */
router.put(
  "/workflows/:workflowId/steps/:stepId/data",
  asyncHandler(async (req, res) => {
    const { workflowId, stepId } = req.params;
    const { data } = req.body;

    try {
      const step = await workflowEngine.updateStepData(
        workflowId,
        stepId,
        data
      );

      res.json({
        id: step.id,
        name: step.name,
        status: step.status,
        data: step.data,
      });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  })
);

module.exports = router;

// app.js
const express = require("express");
const mongoose = require("mongoose");
const bodyParser = require("body-parser");
const workflowRoutes = require("./routes/workflow");

const app = express();

// Middleware
app.use(bodyParser.json());

// Routes
app.use("/api", workflowRoutes);

// Connect to MongoDB
mongoose
  .connect("mongodb://localhost:27017/workflow-system", {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  })
  .then(() => console.log("Connected to MongoDB"))
  .catch((err) => console.error("Failed to connect to MongoDB", err));

// Start server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
```

## Implementation of Specific Steps

Let's implement the first two steps of the workflow as described:

### Step 1: Initial Client Screening & Scoring

This step evaluates potential clients based on predefined criteria through a form-based approach.

#### Step 1.1: Initial Screening Form

```javascript
// services/steps/initialScreeningForm.js
const WorkflowEngine = require("../workflowEngine");

/**
 * Process the initial screening form data
 *
 * @param {string} workflowId - Workflow ID
 * @param {string} stepId - Step ID
 * @param {Object} formData - Form data containing client information
 * @returns {Promise<Object>} Processed form data
 */
async function handleInitialScreeningForm(workflowId, stepId, formData) {
  // Validate required fields
  const requiredFields = [
    "client_name",
    "business_size",
    "strategic_alignment",
    "urgency",
    "willingness_to_change",
    "conflict_of_interest",
  ];

  for (const field of requiredFields) {
    if (!(field in formData)) {
      throw new Error(`Missing required field: ${field}`);
    }
  }

  const workflowEngine = new WorkflowEngine();

  // Update step data with form data
  await workflowEngine.updateStepData(workflowId, stepId, formData);

  // Move to the next step (Score calculation)
  await workflowEngine.completeStep(workflowId, stepId, formData);

  return formData;
}

module.exports = { handleInitialScreeningForm };
```

#### Step 1.2: Score Calculation

```javascript
// services/steps/scoreCalculation.js
const WorkflowEngine = require("../workflowEngine");

/**
 * Calculate scores based on the screening form data
 *
 * @param {string} workflowId - Workflow ID
 * @param {string} stepId - Step ID
 * @param {string} taskId - Task ID
 * @returns {Promise<Object>} Calculated scores
 */
async function handleScoreCalculation(workflowId, stepId, taskId) {
  const workflowEngine = new WorkflowEngine();

  // Get the form data from the previous step (position 0)
  const screeningFormStep = await workflowEngine.getStepByPosition(
    workflowId,
    taskId,
    0
  );
  const formData = screeningFormStep.data;

  // Calculate scores
  const scores = {
    strategic_fit: calculateStrategicFitScore(formData),
    financial_viability: calculateFinancialScore(formData),
    project_feasibility: calculateFeasibilityScore(formData),
    risk_assessment: calculateRiskScore(formData),
    total_score: 0, // Will be calculated below
  };

  // Calculate total score (weighted average)
  const weights = {
    strategic_fit: 0.3,
    financial_viability: 0.25,
    project_feasibility: 0.25,
    risk_assessment: 0.2,
  };

  let totalScore = 0;
  for (const key in weights) {
    totalScore += scores[key] * weights[key];
  }
  scores.total_score = Math.round(totalScore * 100) / 100; // Round to 2 decimal places

  // Update step data with calculated scores
  await workflowEngine.updateStepData(workflowId, stepId, scores);

  // Move to the next step (Screening Outcome)
  await workflowEngine.completeStep(workflowId, stepId, scores);

  return scores;
}

/**
 * Calculate strategic fit score based on form data
 * @param {Object} formData - Form data
 * @returns {number} Strategic fit score (0-100)
 */
function calculateStrategicFitScore(formData) {
  // Example scoring logic
  const strategicAlignment = formData.strategic_alignment || 0;
  const businessSizeScore = getBusinessSizeScore(formData.business_size || "");

  return (strategicAlignment * 0.7 + businessSizeScore * 0.3) * 20; // Scale to 0-100
}

/**
 * Calculate financial viability score
 * @param {Object} formData - Form data
 * @returns {number} Financial viability score (0-100)
 */
function calculateFinancialScore(formData) {
  const financialHealth = formData.financial_health || 0;
  return financialHealth * 20; // Scale to 0-100
}

/**
 * Calculate project feasibility score
 * @param {Object} formData - Form data
 * @returns {number} Project feasibility score (0-100)
 */
function calculateFeasibilityScore(formData) {
  const urgency = formData.urgency || 0;
  const willingness = formData.willingness_to_change || 0;

  return ((urgency + willingness) / 2) * 20; // Scale to 0-100
}

/**
 * Calculate risk assessment score (higher is better/less risky)
 * @param {Object} formData - Form data
 * @returns {number} Risk assessment score (0-100)
 */
function calculateRiskScore(formData) {
  const conflict = formData.conflict_of_interest ? 0 : 5;
  const financialHealth = formData.financial_health || 0;

  return ((conflict + financialHealth) / 2) * 20; // Scale to 0-100
}

/**
 * Convert business size to a score
 * @param {string} size - Business size
 * @returns {number} Score (1-5)
 */
function getBusinessSizeScore(size) {
  const sizeScores = {
    startup: 3,
    small: 4,
    medium: 5,
    large: 4,
    enterprise: 3,
  };

  return sizeScores[size.toLowerCase()] || 3;
}

module.exports = {
  handleScoreCalculation,
  calculateStrategicFitScore,
  calculateFinancialScore,
  calculateFeasibilityScore,
  calculateRiskScore,
  getBusinessSizeScore,
};
```

#### Step 1.3: Screening Outcome

```javascript
// services/steps/screeningOutcome.js
const WorkflowEngine = require("../workflowEngine");

/**
 * Generate screening outcome based on calculated scores
 *
 * @param {string} workflowId - Workflow ID
 * @param {string} stepId - Step ID
 * @param {string} taskId - Task ID
 * @returns {Promise<Object>} Screening outcome data
 */
async function handleScreeningOutcome(workflowId, stepId, taskId) {
  const workflowEngine = new WorkflowEngine();

  // Get the scores from the previous step (position 1)
  const scoreCalculationStep = await workflowEngine.getStepByPosition(
    workflowId,
    taskId,
    1
  );
  const scores = scoreCalculationStep.data;

  // Get the form data from the first step (position 0)
  const screeningFormStep = await workflowEngine.getStepByPosition(
    workflowId,
    taskId,
    0
  );
  const formData = screeningFormStep.data;

  // Determine outcome based on total score
  const totalScore = scores.total_score || 0;

  let classification, recommendation;

  if (totalScore >= 80) {
    classification = "High-potential client";
    recommendation = "Proceed with high priority";
  } else if (totalScore >= 60) {
    classification = "Promising client";
    recommendation = "Proceed normally";
  } else if (totalScore >= 40) {
    classification = "Average potential";
    recommendation = "Proceed with caution";
  } else {
    classification = "Low readiness";
    recommendation = "Consider carefully before proceeding";
  }

  // Generate summary
  const clientName = formData.client_name || "The client";
  let summary = `${clientName} shows a total score of ${totalScore}/100. `;

  // Add details about specific scores
  if (scores.strategic_fit >= 70) {
    summary += "Strong strategic alignment. ";
  } else {
    summary += "Limited strategic alignment. ";
  }

  if (scores.financial_viability >= 70) {
    summary += "Good financial viability. ";
  } else {
    summary += "Financial concerns noted. ";
  }

  if (formData.conflict_of_interest) {
    summary += "Potential conflict of interest identified. ";
  } else {
    summary += "No ethical conflicts identified. ";
  }

  // Create outcome data
  const outcome = {
    classification,
    recommendation,
    summary,
    scores,
  };

  // Update step data with outcome
  await workflowEngine.updateStepData(workflowId, stepId, outcome);

  // Complete the step and the task
  await workflowEngine.completeStep(workflowId, stepId, outcome);

  return outcome;
}

module.exports = { handleScreeningOutcome };
```

### Step 2: Automated Client Brief/RFP Analysis

This step analyzes client briefs and RFPs using automated tools.

```javascript
// services/steps/clientBriefAnalysis.js
const WorkflowEngine = require("../workflowEngine");
const pdfParse = require("pdf-parse");
const mammoth = require("mammoth");

/**
 * Analyze client brief or RFP documents
 *
 * @param {string} workflowId - Workflow ID
 * @param {string} stepId - Step ID
 * @param {Array} uploadedFiles - List of file objects
 * @returns {Promise<Object>} Analysis results
 */
async function handleClientBriefAnalysis(workflowId, stepId, uploadedFiles) {
  const workflowEngine = new WorkflowEngine();

  // Process each uploaded file
  const extractedTexts = [];

  for (const file of uploadedFiles) {
    try {
      // Extract text from file based on content type
      let text = "";

      if (file.mimetype === "application/pdf") {
        text = await extractTextFromPdf(file.buffer);
      } else if (file.mimetype.startsWith("text/")) {
        text = file.buffer.toString("utf-8");
      } else if (
        file.mimetype.startsWith(
          "application/vnd.openxmlformats-officedocument.wordprocessingml"
        )
      ) {
        text = await extractTextFromDocx(file.buffer);
      } else {
        continue; // Skip unsupported file types
      }

      extractedTexts.push({
        filename: file.originalname,
        text,
      });
    } catch (error) {
      console.error(`Error processing file ${file.originalname}:`, error);
    }
  }

  // Combine all extracted texts
  const combinedText = extractedTexts.map((item) => item.text).join("\n\n");

  // Analyze the text using AI
  const analysisResults = await analyzeClientBrief(combinedText);

  // Update step data with analysis results
  await workflowEngine.updateStepData(workflowId, stepId, analysisResults);

  // Complete the step
  await workflowEngine.completeStep(workflowId, stepId, analysisResults);

  return analysisResults;
}

/**
 * Extract text from PDF file
 * @param {Buffer} buffer - PDF file buffer
 * @returns {Promise<string>} Extracted text
 */
async function extractTextFromPdf(buffer) {
  try {
    const data = await pdfParse(buffer);
    return data.text;
  } catch (error) {
    console.error("Error extracting text from PDF:", error);
    return "Error extracting text from PDF";
  }
}

/**
 * Extract text from DOCX file
 * @param {Buffer} buffer - DOCX file buffer
 * @returns {Promise<string>} Extracted text
 */
async function extractTextFromDocx(buffer) {
  try {
    const result = await mammoth.extractRawText({ buffer });
    return result.value;
  } catch (error) {
    console.error("Error extracting text from DOCX:", error);
    return "Error extracting text from DOCX";
  }
}

/**
 * Analyze client brief text using AI
 * @param {string} text - Combined text from all documents
 * @returns {Promise<Object>} Analysis results
 */
async function analyzeClientBrief(text) {
  // In a real implementation, this would call an AI service like OpenAI
  // This is a simplified placeholder

  // Generate an executive summary
  const executiveSummary =
    "This is an AI-generated summary of the client brief.";

  // Extract key information for the Discovery Questionnaire
  const discoveryData = {
    vision_mission_values: await extractVisionMissionValues(text),
    project_goals: await extractProjectGoals(text),
    target_customers: await extractTargetCustomers(text),
    challenges: await extractChallenges(text),
    competitors: await extractCompetitors(text),
    timeline_budget: await extractTimelineBudget(text),
  };

  // Determine confidence levels for each extracted piece
  const confidenceLevels = {
    vision_mission_values: determineConfidence(
      "vision_mission_values",
      discoveryData
    ),
    project_goals: determineConfidence("project_goals", discoveryData),
    target_customers: determineConfidence("target_customers", discoveryData),
    challenges: determineConfidence("challenges", discoveryData),
    competitors: determineConfidence("competitors", discoveryData),
    timeline_budget: determineConfidence("timeline_budget", discoveryData),
  };

  return {
    executive_summary: executiveSummary,
    discovery_data: discoveryData,
    confidence_levels: confidenceLevels,
    missing_information: identifyMissingInformation(discoveryData),
  };
}

/**
 * Extract vision, mission, and values from text
 * @param {string} text - Document text
 * @returns {Promise<string>} Extracted information
 */
async function extractVisionMissionValues(text) {
  // This would use NLP or AI to extract this information
  // Placeholder implementation
  return "Company aims to be a leader in sustainable packaging solutions.";
}

/**
 * Extract project goals from text
 * @param {string} text - Document text
 * @returns {Promise<string>} Extracted information
 */
async function extractProjectGoals(text) {
  return "Increase brand awareness by 20% within one year.";
}

/**
 * Extract target customers from text
 * @param {string} text - Document text
 * @returns {Promise<string>} Extracted information
 */
async function extractTargetCustomers(text) {
  return "Environmentally conscious consumers aged 25-45.";
}

/**
 * Extract challenges from text
 * @param {string} text - Document text
 * @returns {Promise<string>} Extracted information
 */
async function extractChallenges(text) {
  return "Fragmented brand identity across regions.";
}

/**
 * Extract competitors from text
 * @param {string} text - Document text
 * @returns {Promise<string>} Extracted information
 */
async function extractCompetitors(text) {
  return "Major competitors include EcoPack and GreenWrap.";
}

/**
 * Extract timeline and budget information from text
 * @param {string} text - Document text
 * @returns {Promise<string>} Extracted information
 */
async function extractTimelineBudget(text) {
  return "6-month timeline with a budget of $50,000-$75,000.";
}

/**
 * Determine confidence level for extracted data
 * @param {string} field - Field name
 * @param {Object} data - Discovery data
 * @returns {string} Confidence level (high, low, not_found)
 */
function determineConfidence(field, data) {
  // In a real implementation, this would be based on AI confidence scores
  // Placeholder implementation
  if (!data[field]) {
    return "not_found";
  } else if (data[field].length > 50) {
    return "high";
  } else {
    return "low";
  }
}

/**
 * Identify missing information in the discovery data
 * @param {Object} discoveryData - Discovery data
 * @returns {Array} List of missing fields
 */
function identifyMissingInformation(discoveryData) {
  const missing = [];

  for (const key in discoveryData) {
    if (!discoveryData[key]) {
      missing.push(key);
    }
  }

  return missing;
}

module.exports = {
  handleClientBriefAnalysis,
  extractTextFromPdf,
  extractTextFromDocx,
  analyzeClientBrief,
};
```

#### API Route for File Upload and Analysis

```javascript
// routes/fileAnalysis.js
const express = require("express");
const router = express.Router();
const multer = require("multer");
const {
  handleClientBriefAnalysis,
} = require("../services/steps/clientBriefAnalysis");

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
});

/**
 * Upload and analyze client brief/RFP documents
 * POST /api/workflows/:workflowId/steps/:stepId/analyze-documents
 */
router.post(
  "/workflows/:workflowId/steps/:stepId/analyze-documents",
  upload.array("files", 5), // Allow up to 5 files
  async (req, res) => {
    try {
      const { workflowId, stepId } = req.params;
      const files = req.files;

      if (!files || files.length === 0) {
        return res.status(400).json({ error: "No files uploaded" });
      }

      const analysisResults = await handleClientBriefAnalysis(
        workflowId,
        stepId,
        files
      );

      res.json({
        message: "Files analyzed successfully",
        results: analysisResults,
      });
    } catch (error) {
      console.error("Error analyzing files:", error);
      res.status(500).json({ error: error.message });
    }
  }
);

module.exports = router;

// app.js - Add this route to the main app
const fileAnalysisRoutes = require("./routes/fileAnalysis");
app.use("/api", fileAnalysisRoutes);
```

## Frontend Implementation

For the frontend, we'll need components to interact with each step of the workflow:

```typescript
// WorkflowContainer.tsx - Main workflow container component
import React, { useState, useEffect } from "react";
import InitialScreeningForm from "./steps/InitialScreeningForm";
import ScoreCalculation from "./steps/ScoreCalculation";
import ScreeningOutcome from "./steps/ScreeningOutcome";
import ClientBriefAnalysis from "./steps/ClientBriefAnalysis";

interface WorkflowProps {
  workflowId: string;
}

const WorkflowContainer: React.FC<WorkflowProps> = ({ workflowId }) => {
  const [workflow, setWorkflow] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch workflow data
  useEffect(() => {
    const fetchWorkflow = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/workflows/${workflowId}`);
        if (!response.ok) {
          throw new Error("Failed to fetch workflow");
        }
        const data = await response.json();
        setWorkflow(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchWorkflow();
  }, [workflowId]);

  // Render appropriate component based on current task and step
  const renderCurrentStep = () => {
    if (!workflow) return null;

    // Find the current task (first pending or in_progress)
    const currentTask = workflow.tasks.find(
      (task) => task.status === "pending" || task.status === "in_progress"
    );

    if (!currentTask) {
      return <div>All tasks completed!</div>;
    }

    // Find the current step within the task
    const currentStep = currentTask.steps.find(
      (step) => step.status === "pending" || step.status === "in_progress"
    );

    if (!currentStep) {
      return (
        <div>All steps in this task completed. Ready to start next task.</div>
      );
    }

    // Render component based on task and step
    if (currentTask.id === "1") {
      // Initial Client Screening & Scoring
      if (currentStep.id === "1.1") {
        return <InitialScreeningForm stepId={currentStep.id} />;
      } else if (currentStep.id === "1.2") {
        return <ScoreCalculation stepId={currentStep.id} />;
      } else if (currentStep.id === "1.3") {
        return <ScreeningOutcome stepId={currentStep.id} />;
      }
    } else if (currentTask.id === "2") {
      // Automated Client Brief/RFP Analysis
      return <ClientBriefAnalysis stepId={currentStep.id} />;
    }

    return <div>Unknown step</div>;
  };

  if (loading) return <div>Loading workflow...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="workflow-container">
      <h1>{workflow.name}</h1>
      <p>{workflow.description}</p>

      {/* Workflow progress indicator */}
      <div className="workflow-progress">
        {workflow.tasks.map((task) => (
          <div
            key={task.id}
            className={`task-indicator ${task.status}`}
            title={task.name}
          >
            {task.id}
          </div>
        ))}
      </div>

      {/* Current step component */}
      <div className="current-step">{renderCurrentStep()}</div>
    </div>
  );
};

export default WorkflowContainer;
```

## Conclusion

This implementation provides a simplified workflow system based on Dify's architecture but tailored for a predefined, linear flow with human approval/triggering for each step. The implementation uses Node.js and MongoDB for the backend, with Express.js for the API layer.

### Key Differences from Dify's Workflow System

1. **Predefined Flow**: Unlike Dify's dynamic graph-based workflow, this implementation uses a fixed, sequential flow of tasks and steps.

2. **Human Approval**: Each step requires explicit human approval before proceeding to the next step, enforcing a controlled progression through the workflow.

3. **Simplified Architecture**: The implementation focuses on a straightforward task/step model without the complexity of Dify's node types and variable pools.

4. **Task-Specific Logic**: Each step has custom logic tailored to its specific requirements, rather than using generic node types.

### Technology Stack

- **Backend**: Node.js with Express.js
- **Database**: MongoDB with Mongoose ODM
- **File Processing**: pdf-parse for PDF extraction, mammoth for DOCX extraction
- **File Uploads**: multer for handling multipart/form-data
- **Frontend**: React with TypeScript

### Key Features

- **Sequential Progression**: Tasks and steps are completed in a predefined order.
- **Data Persistence**: All form inputs and processing results are stored in MongoDB.
- **Asynchronous Processing**: Promises and async/await for handling asynchronous operations.
- **State Management**: The workflow engine tracks the current state of each task and step.
- **Human-in-the-Loop**: Each step transition requires explicit human action.
- **Task-Specific UI Components**: The frontend renders different components based on the current task and step.
- **File Processing**: Support for analyzing uploaded documents (PDF, DOCX, text).

### Implementation Benefits

1. **Simplicity**: Easier to understand and maintain than a fully dynamic workflow system.
2. **Predictability**: The workflow follows a consistent, predefined path.
3. **Control**: Human approval ensures quality control at each step.
4. **Flexibility**: Each step can implement custom logic while maintaining the overall workflow structure.
5. **Scalability**: Node.js and MongoDB provide a scalable foundation for handling multiple workflows.
6. **Modularity**: Each step is implemented as a separate module, making it easy to add or modify steps.

### Deployment Considerations

1. **Environment Variables**: Store configuration like database connection strings in environment variables.
2. **Error Handling**: Implement comprehensive error handling and logging.
3. **Authentication**: Add JWT or session-based authentication for secure access.
4. **Rate Limiting**: Implement rate limiting for API endpoints to prevent abuse.
5. **Monitoring**: Add monitoring and logging for production deployments.

This implementation provides a solid foundation for the client screening and analysis workflow, with the ability to extend to additional steps as needed. The Node.js implementation offers good performance and scalability, while MongoDB provides flexible schema design that can adapt as the workflow requirements evolve.
