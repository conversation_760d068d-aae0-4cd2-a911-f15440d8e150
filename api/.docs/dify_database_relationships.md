# Dify Database Relationships

## Overview

Dify uses a relational database (PostgreSQL) to store application data. The database schema is organized around several key entities that represent the core concepts of the platform. This document explains the main database entities and their relationships.

## Entity Relationship Diagram

```
┌───────────────┐       ┌───────────────┐       ┌───────────────┐
│    Tenant     │       │    Account    │       │  TenantAccount│
│               │◄──────┤               │◄──────┤     Join      │
└───────┬───────┘       └───────────────┘       └───────────────┘
        │
        │
        ▼
┌───────────────┐       ┌───────────────┐       ┌───────────────┐
│      App      │◄──────┤  AppModelConfig│       │     Site      │
│               │       │               │       │               │
└───────┬───────┘       └───────────────┘       └───────┬───────┘
        │                                               │
        │                                               │
        ▼                                               │
┌───────────────┐       ┌───────────────┐               │
│   Workflow    │       │ Conversation  │◄──────────────┘
│               │◄──────┤               │
└───────┬───────┘       └───────┬───────┘
        │                       │
        │                       │
        ▼                       ▼
┌───────────────┐       ┌───────────────┐       ┌───────────────┐
│  WorkflowRun  │       │    Message    │◄──────┤MessageFeedback│
│               │       │               │       │               │
└───────┬───────┘       └───────┬───────┘       └───────────────┘
        │                       │
        │                       │
        ▼                       │
┌───────────────┐               │
│WorkflowNodeExe│               │
│    cution     │               │
└───────────────┘               │
                                │
┌───────────────┐               │
│    Dataset    │               │
│               │               │
└───────┬───────┘               │
        │                       │
        │                       │
        ▼                       │
┌───────────────┐               │
│   Document    │               │
│               │               │
└───────┬───────┘               │
        │                       │
        │                       │
        ▼                       │
┌───────────────┐               │
│DocumentSegment│               │
│               │               │
└───────────────┘               │
                                │
┌───────────────┐               │
│   Provider    │               │
│               │               │
└───────┬───────┘               │
        │                       │
        │                       │
        ▼                       │
┌───────────────┐               │
│ProviderModel  │               │
│               │               │
└───────────────┘               │
                                │
┌───────────────┐               │
│    EndUser    │◄──────────────┘
│               │
└───────────────┘
```

## Main Entities

### Tenant and Account

- **Tenant**: Represents a workspace or organization
- **Account**: Represents a user account
- **TenantAccountJoin**: Links accounts to tenants with specific roles

```python
class Tenant(Base):
    id = db.Column(StringUUID, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    status = db.Column(db.String(255), nullable=False)
    
class Account(Base):
    id = db.Column(StringUUID, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(255), nullable=False)
    
class TenantAccountJoin(Base):
    tenant_id = db.Column(StringUUID, db.ForeignKey('tenant.id'), primary_key=True)
    account_id = db.Column(StringUUID, db.ForeignKey('account.id'), primary_key=True)
    role = db.Column(db.String(255), nullable=False)
```

### App and Configuration

- **App**: Represents an AI application
- **AppModelConfig**: Stores the model configuration for an app
- **Site**: Stores the public site configuration for an app

```python
class App(Base):
    id = db.Column(StringUUID, primary_key=True)
    tenant_id = db.Column(StringUUID, nullable=False)
    name = db.Column(db.String(255), nullable=False)
    mode = db.Column(db.String(255), nullable=False)
    app_model_config_id = db.Column(StringUUID, nullable=True)
    workflow_id = db.Column(StringUUID, nullable=True)
    
class AppModelConfig(Base):
    id = db.Column(StringUUID, primary_key=True)
    app_id = db.Column(StringUUID, nullable=False)
    model = db.Column(db.Text)
    
class Site(Base):
    id = db.Column(StringUUID, primary_key=True)
    app_id = db.Column(StringUUID, nullable=False)
    title = db.Column(db.String(255), nullable=False)
```

### Workflow System

- **Workflow**: Stores the workflow definition
- **WorkflowRun**: Represents an execution of a workflow
- **WorkflowNodeExecution**: Represents the execution of a single node in a workflow

```python
class Workflow(Base):
    id = db.Column(StringUUID, primary_key=True)
    tenant_id = db.Column(StringUUID, nullable=False)
    app_id = db.Column(StringUUID, nullable=False)
    type = db.Column(db.String(255), nullable=False)
    version = db.Column(db.String(255), nullable=False)
    graph = db.Column(db.Text)
    
class WorkflowRun(Base):
    id = db.Column(StringUUID, primary_key=True)
    tenant_id = db.Column(StringUUID)
    app_id = db.Column(StringUUID)
    workflow_id = db.Column(StringUUID)
    status = db.Column(db.String(255))
    
class WorkflowNodeExecution(Base):
    id = db.Column(StringUUID, primary_key=True)
    tenant_id = db.Column(StringUUID)
    app_id = db.Column(StringUUID)
    workflow_id = db.Column(StringUUID)
    workflow_run_id = db.Column(StringUUID)
    node_id = db.Column(db.String(255))
    node_type = db.Column(db.String(255))
    status = db.Column(db.String(255))
```

### Conversation and Messages

- **Conversation**: Represents a conversation with an AI app
- **Message**: Represents a single message in a conversation
- **MessageFeedback**: Stores feedback on messages

```python
class Conversation(Base):
    id = db.Column(StringUUID, primary_key=True)
    app_id = db.Column(StringUUID, nullable=False)
    name = db.Column(db.String(255), nullable=False)
    
class Message(Base):
    id = db.Column(StringUUID, primary_key=True)
    app_id = db.Column(StringUUID, nullable=False)
    conversation_id = db.Column(StringUUID, db.ForeignKey("conversations.id"), nullable=False)
    query = db.Column(db.Text, nullable=False)
    
class MessageFeedback(Base):
    id = db.Column(StringUUID, primary_key=True)
    message_id = db.Column(StringUUID, nullable=False)
    rating = db.Column(db.String(255), nullable=False)
```

### Knowledge Base

- **Dataset**: Represents a knowledge dataset
- **Document**: Represents a document in a dataset
- **DocumentSegment**: Represents a segment of a document

```python
class Dataset(Base):
    id = db.Column(StringUUID, primary_key=True)
    tenant_id = db.Column(StringUUID, nullable=False)
    name = db.Column(db.String(255), nullable=False)
    
class Document(Base):
    id = db.Column(StringUUID, primary_key=True)
    tenant_id = db.Column(StringUUID, nullable=False)
    dataset_id = db.Column(StringUUID, nullable=False)
    name = db.Column(db.String(255), nullable=False)
    
class DocumentSegment(Base):
    id = db.Column(StringUUID, primary_key=True)
    tenant_id = db.Column(StringUUID, nullable=False)
    dataset_id = db.Column(StringUUID, nullable=False)
    document_id = db.Column(StringUUID, nullable=False)
    content = db.Column(db.Text, nullable=False)
```

### Model Providers

- **Provider**: Represents an LLM provider
- **ProviderModel**: Represents a model from a provider

```python
class Provider(Base):
    id = db.Column(StringUUID, primary_key=True)
    tenant_id = db.Column(StringUUID, nullable=False)
    provider_name = db.Column(db.String(255), nullable=False)
    
class ProviderModel(Base):
    id = db.Column(StringUUID, primary_key=True)
    tenant_id = db.Column(StringUUID, nullable=False)
    provider_name = db.Column(db.String(255), nullable=False)
    model_name = db.Column(db.String(255), nullable=False)
```

### End Users

- **EndUser**: Represents an end user of an AI app

```python
class EndUser(Base):
    id = db.Column(StringUUID, primary_key=True)
    tenant_id = db.Column(StringUUID, nullable=False)
    app_id = db.Column(StringUUID, nullable=False)
    type = db.Column(db.String(255), nullable=False)
```

## Key Relationships

1. **Tenant to App**: One-to-many (A tenant can have multiple apps)
2. **App to Workflow**: One-to-many (An app can have multiple workflows)
3. **App to Conversation**: One-to-many (An app can have multiple conversations)
4. **Conversation to Message**: One-to-many (A conversation can have multiple messages)
5. **Workflow to WorkflowRun**: One-to-many (A workflow can have multiple runs)
6. **WorkflowRun to WorkflowNodeExecution**: One-to-many (A workflow run can have multiple node executions)
7. **Dataset to Document**: One-to-many (A dataset can have multiple documents)
8. **Document to DocumentSegment**: One-to-many (A document can have multiple segments)
9. **Provider to ProviderModel**: One-to-many (A provider can have multiple models)

## Database Configuration

Dify uses SQLAlchemy as the ORM (Object-Relational Mapping) layer:

```python
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import MetaData

POSTGRES_INDEXES_NAMING_CONVENTION = {
    "ix": "%(column_0_label)s_idx",
    "uq": "%(table_name)s_%(column_0_name)s_key",
    "ck": "%(table_name)s_%(constraint_name)s_check",
    "fk": "%(table_name)s_%(column_0_name)s_fkey",
    "pk": "%(table_name)s_pkey",
}

metadata = MetaData(naming_convention=POSTGRES_INDEXES_NAMING_CONVENTION)
db = SQLAlchemy(metadata=metadata)
```

## Conclusion

The Dify database schema is designed to support the core functionality of the platform, including app management, workflow execution, conversation handling, and knowledge management. The relationships between entities reflect the hierarchical nature of the platform, with tenants at the top level and various components organized beneath them.
