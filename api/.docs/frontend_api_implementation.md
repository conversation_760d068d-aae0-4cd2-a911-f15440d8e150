# Frontend API Implementation in Dify

This document explains how the frontend API implementation works in Dify, focusing on the custom request function in `web/service/base.ts` and its integration with TanStack Query.

## Overview

Dify's frontend API implementation uses a custom HTTP client built on top of the `ky` library, rather than directly using `fetch()` or `axios`. This custom implementation is then integrated with TanStack Query (formerly React Query) for data fetching, caching, and state management.

## Core Components

### 1. Base HTTP Client (`web/service/fetch.ts`)

The foundation of the API implementation is a custom HTTP client built using the `ky` library:

```typescript
// web/service/fetch.ts
import ky from "ky";

// ...

async function base<T>(
  url: string,
  options: FetchOptionType = {},
  otherOptions: IOtherOptions = {}
): Promise<T> {
  // Configure request options
  const { params, body, headers, ...init } = Object.assign(
    {},
    baseOptions,
    options
  );
  const {
    isPublicAPI = false,
    isMarketplaceAPI = false,
    bodyStringify = true,
    needAllResponseContent,
    deleteContentType,
    getAbortController,
  } = otherOptions;

  // Determine API prefix
  let base: string;
  if (isMarketplaceAPI) base = MARKETPLACE_API_PREFIX;
  else if (isPublicAPI) base = PUBLIC_API_PREFIX;
  else base = API_PREFIX;

  // Configure abort controller if needed
  if (getAbortController) {
    const abortController = new AbortController();
    getAbortController(abortController);
    options.signal = abortController.signal;
  }

  // Build full URL
  const fetchPathname = base + (url.startsWith("/") ? url : `/${url}`);

  // Create extended client with hooks
  const client = baseClient.extend({
    hooks: {
      // ... hooks configuration
    },
  });

  // Make the request
  const res = await client(fetchPathname, {
    // ... request configuration
  });

  // Process response
  if (needAllResponseContent) return res as T;

  const contentType = res.headers.get("content-type");
  if (
    contentType &&
    [ContentType.download, ContentType.audio, ContentType.downloadZip].includes(
      contentType
    )
  )
    return (await res.blob()) as T;

  return (await res.json()) as T;
}
```

### 2. Request Function (`web/service/base.ts`)

The `request` function in `base.ts` wraps the base HTTP client and adds error handling, authentication, and other features:

```typescript
// web/service/base.ts
export const request = async <T>(
  url: string,
  options = {},
  otherOptions?: IOtherOptions
) => {
  try {
    const otherOptionsForBaseFetch = otherOptions || {};
    const [err, resp] = await asyncRunSafe<T>(
      baseFetch(url, options, otherOptionsForBaseFetch)
    );

    if (err === null) return resp;

    const errResp: Response = err as any;

    // Handle 401 Unauthorized errors
    if (errResp.status === 401) {
      // Token refresh logic
      const [refreshErr] = await asyncRunSafe(
        refreshAccessTokenOrRelogin(TIME_OUT)
      );
      if (refreshErr === null)
        return baseFetch<T>(url, options, otherOptionsForBaseFetch);

      // Redirect to login if token refresh fails
      // ...
    } else {
      return Promise.reject(err);
    }
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};
```

### 3. HTTP Method Wrappers

The base `request` function is wrapped with method-specific functions:

```typescript
// web/service/base.ts
export const get = <T>(
  url: string,
  options = {},
  otherOptions?: IOtherOptions
) => {
  return request<T>(
    url,
    Object.assign({}, options, { method: "GET" }),
    otherOptions
  );
};

export const post = <T>(
  url: string,
  options = {},
  otherOptions?: IOtherOptions
) => {
  return request<T>(
    url,
    Object.assign({}, options, { method: "POST" }),
    otherOptions
  );
};

// Additional methods: put, del, patch, etc.
```

### 4. TanStack Query Integration

The custom HTTP client is integrated with TanStack Query through custom hooks:

```typescript
// web/service/use-apps.ts
import { get } from "./base";
import { useQuery } from "@tanstack/react-query";

const NAME_SPACE = "apps";

const useAppFullListKey = [NAME_SPACE, "full-list"];
export const useAppFullList = () => {
  return useQuery<AppListResponse>({
    queryKey: useAppFullListKey,
    queryFn: () =>
      get<AppListResponse>("/apps", { params: { page: 1, limit: 100 } }),
  });
};
```

## Key Features

### 1. Type Safety

The implementation uses TypeScript generics to provide type safety:

```typescript
// Example of type-safe API call
const appDetail = await get<App>(`/apps/${appID}`);
```

### 2. Error Handling

Comprehensive error handling with automatic token refresh:

```typescript
// Automatic token refresh on 401 errors
if (errResp.status === 401) {
  const [refreshErr] = await asyncRunSafe(
    refreshAccessTokenOrRelogin(TIME_OUT)
  );
  if (refreshErr === null)
    return baseFetch<T>(url, options, otherOptionsForBaseFetch);
  // ...
}
```

### 3. Request Cancellation

Support for aborting requests:

```typescript
// Support for abort controller
if (getAbortController) {
  const abortController = new AbortController();
  getAbortController(abortController);
  options.signal = abortController.signal;
}
```

### 4. SSE (Server-Sent Events) Support

Special handling for streaming responses:

```typescript
// web/service/base.ts
export const ssePost = async (
  url: string,
  fetchOptions: FetchOptionType,
  otherOptions: IOtherOptions
) => {
  // ... configuration

  globalThis.fetch(urlWithPrefix, options as RequestInit).then((res) => {
    // ... error handling

    return handleStream(
      res,
      (str: string, isFirstMessage: boolean, moreInfo: IOnDataMoreInfo) => {
        // ... stream handling
      }
    );
  });
};
```

### 5. Query Invalidation

Utility functions for invalidating queries:

```typescript
// web/service/use-base.ts
export const useInvalid = (key: QueryKey) => {
  const queryClient = useQueryClient();
  return () => {
    queryClient.invalidateQueries({
      queryKey: key,
    });
  };
};
```

## TanStack Query Configuration

The TanStack Query client is configured in `web/context/query-client.tsx`:

```typescript
// web/context/query-client.tsx
const STALE_TIME = 1000 * 60 * 30; // 30 minutes

const client = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: STALE_TIME,
    },
  },
});

export const TanstackQueryIniter: FC<PropsWithChildren> = (props) => {
  const { children } = props;
  return (
    <QueryClientProvider client={client}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
};
```

## Usage Examples

### Basic Query

```typescript
// Example of a basic query hook
export const useAppDetail = (appID: string) => {
  return useQuery<App>({
    queryKey: [NAME_SPACE, "detail", appID],
    queryFn: () => get<App>(`/apps/${appID}`),
  });
};
```

### Mutation

```typescript
// Example of a mutation hook
export const useGenerateStructuredOutputRules = () => {
  return useMutation({
    mutationKey: [NAME_SPACE, "generate-structured-output-rules"],
    mutationFn: (body: StructuredOutputRulesRequestBody) => {
      return post<StructuredOutputRulesResponse>(
        "/rule-structured-output-generate",
        { body }
      );
    },
  });
};
```

## Advanced Features

### Authentication Flow

The implementation handles authentication tokens in a sophisticated way:

1. **Token Storage**: Access tokens are stored in localStorage

   ```typescript
   // For regular API calls
   const accessToken = localStorage.getItem("console_token") || "";

   // For public API calls
   const sharedToken = globalThis.location.pathname.split("/").slice(-1)[0];
   const userId = (await getProcessedSystemVariablesFromUrlParams()).user_id;
   const accessToken =
     localStorage.getItem("token") || JSON.stringify({ version: 2 });
   ```

2. **Token Refresh**: Automatic token refresh when receiving 401 errors
   ```typescript
   if (errResp.status === 401) {
     const [refreshErr] = await asyncRunSafe(
       refreshAccessTokenOrRelogin(TIME_OUT)
     );
     if (refreshErr === null)
       return baseFetch<T>(url, options, otherOptionsForBaseFetch);
     // ...
   }
   ```

### Stream Handling

The implementation has special handling for streaming responses, particularly useful for AI-generated content:

```typescript
const handleStream = (
  response: Response,
  onData: IOnData,
  onCompleted?: IOnCompleted
  // ... other callbacks
) => {
  if (!response.ok) throw new Error("Network response was not ok");

  const reader = response.body?.getReader();
  const decoder = new TextDecoder("utf-8");
  let buffer = "";
  let bufferObj: Record<string, any>;
  let isFirstMessage = true;

  function read() {
    let hasError = false;
    reader?.read().then((result: any) => {
      if (result.done) {
        onCompleted && onCompleted();
        return;
      }
      buffer += decoder.decode(result.value, { stream: true });
      const lines = buffer.split("\n");

      // Process each line of the stream
      try {
        lines.forEach((message) => {
          if (message.startsWith("data: ")) {
            try {
              bufferObj = JSON.parse(message.substring(6));
            } catch {
              // Handle message cut-off
              return;
            }

            // Handle different event types
            if (
              bufferObj.event === "message" ||
              bufferObj.event === "agent_message"
            ) {
              onData(unicodeToChar(bufferObj.answer), isFirstMessage, {
                conversationId: bufferObj.conversation_id,
                taskId: bufferObj.task_id,
                messageId: bufferObj.id,
              });
              isFirstMessage = false;
            } else if (bufferObj.event === "agent_thought") {
              onThought?.(bufferObj as ThoughtItem);
            }
            // ... handle other event types
          }
        });
        buffer = lines[lines.length - 1];
      } catch (e) {
        // Handle errors
      }

      if (!hasError) read();
    });
  }
  read();
};
```

### Integration with TanStack Query's Cache System

The implementation leverages TanStack Query's cache system for efficient data management:

1. **Namespace Organization**: API calls are organized by namespaces for better cache management

   ```typescript
   const NAME_SPACE = "apps";
   const useAppFullListKey = [NAME_SPACE, "full-list"];
   ```

2. **Cache Invalidation**: Utility functions for invalidating specific parts of the cache

   ```typescript
   export const useInvalidateAppFullList = () => {
     return useInvalid(useAppFullListKey);
   };

   // Implementation of useInvalid
   export const useInvalid = (key: QueryKey) => {
     const queryClient = useQueryClient();
     return () => {
       queryClient.invalidateQueries({ queryKey: key });
     };
   };
   ```

3. **Cache Reset**: Utility for completely resetting parts of the cache
   ```typescript
   export const useReset = (key: QueryKey) => {
     const queryClient = useQueryClient();
     return () => {
       queryClient.resetQueries({ queryKey: key });
     };
   };
   ```

### Error Handling with asyncRunSafe

The implementation uses a custom `asyncRunSafe` utility to handle promises in a way that avoids try/catch blocks:

```typescript
// Usage in request function
const [err, resp] = await asyncRunSafe<T>(
  baseFetch(url, options, otherOptionsForBaseFetch)
);
if (err === null) return resp;
```

This pattern returns a tuple of `[error, result]` where one of them is always null, making error handling more explicit and readable.

## Why This Approach?

Dify's approach to API implementation offers several advantages:

1. **Abstraction Layer**: By using a custom request function instead of directly using `fetch()` or `axios`, the implementation provides a consistent abstraction layer that can be modified without affecting the rest of the application.

2. **Centralized Authentication**: Authentication logic is centralized, making it easier to maintain and update.

3. **Unified Error Handling**: Error handling is consistent across all API calls, with special handling for authentication errors.

4. **Type Safety**: TypeScript generics provide end-to-end type safety from API calls to component rendering.

5. **Efficient Caching**: Integration with TanStack Query provides efficient caching and state management.

6. **Stream Support**: Built-in support for streaming responses, which is essential for AI-generated content.

## Conclusion

Dify's frontend API implementation provides a robust, type-safe, and feature-rich approach to handling API requests. By building on top of the `ky` library and integrating with TanStack Query, it offers excellent developer experience with built-in caching, error handling, and state management.

The custom request function serves as the foundation, providing consistent error handling and authentication across all API calls, while TanStack Query adds powerful data synchronization capabilities. This approach is particularly well-suited for applications with complex data requirements and real-time streaming needs, such as AI-powered platforms.
